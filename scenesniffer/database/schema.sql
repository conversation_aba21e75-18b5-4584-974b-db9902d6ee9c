-- SceneSniffer Database Schema
-- This file contains the complete database schema for the SceneSniffer platform

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    username VARCHAR(50) UNIQUE,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User preferences table
CREATE TABLE public.user_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    genres TEXT[] DEFAULT '{}',
    streaming_services TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Creators table
CREATE TABLE public.creators (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    platform VARCHAR(50) NOT NULL CHECK (platform IN ('youtube', 'instagram', 'twitter', 'tiktok')),
    handle VARCHAR(255) NOT NULL,
    avatar_url TEXT,
    follower_count INTEGER DEFAULT 0,
    verified BOOLEAN DEFAULT FALSE,
    trust_score DECIMAL(3,2) DEFAULT 5.0 CHECK (trust_score >= 0 AND trust_score <= 10),
    genres TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(platform, handle)
);

-- User-Creator relationships (following)
CREATE TABLE public.user_creators (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    creator_id UUID REFERENCES public.creators(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, creator_id)
);

-- Content table (aggregated from various platforms)
CREATE TABLE public.content (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    creator_id UUID REFERENCES public.creators(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    content_type VARCHAR(50) NOT NULL CHECK (content_type IN ('review', 'theory', 'news', 'spoiler-free', 'breakdown', 'recommendation')),
    platform_url TEXT NOT NULL,
    platform_id VARCHAR(255) NOT NULL,
    thumbnail_url TEXT,
    published_at TIMESTAMP WITH TIME ZONE NOT NULL,
    ai_summary TEXT,
    referenced_titles TEXT[] DEFAULT '{}',
    tags TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(creator_id, platform_id)
);

-- Movies table (from TMDb)
CREATE TABLE public.movies (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    tmdb_id INTEGER UNIQUE NOT NULL,
    title VARCHAR(500) NOT NULL,
    overview TEXT,
    poster_path VARCHAR(255),
    backdrop_path VARCHAR(255),
    release_date DATE,
    genres TEXT[] DEFAULT '{}',
    runtime INTEGER,
    vote_average DECIMAL(3,1),
    vote_count INTEGER,
    imdb_id VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- TV Shows table (from TMDb)
CREATE TABLE public.tv_shows (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    tmdb_id INTEGER UNIQUE NOT NULL,
    name VARCHAR(500) NOT NULL,
    overview TEXT,
    poster_path VARCHAR(255),
    backdrop_path VARCHAR(255),
    first_air_date DATE,
    last_air_date DATE,
    genres TEXT[] DEFAULT '{}',
    episode_run_time INTEGER[],
    vote_average DECIMAL(3,1),
    vote_count INTEGER,
    number_of_seasons INTEGER,
    number_of_episodes INTEGER,
    status VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Watchlist table
CREATE TABLE public.watchlist (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    content_type VARCHAR(10) NOT NULL CHECK (content_type IN ('movie', 'tv')),
    tmdb_id INTEGER NOT NULL,
    title VARCHAR(500) NOT NULL,
    poster_path VARCHAR(255),
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    watched BOOLEAN DEFAULT FALSE,
    watched_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, content_type, tmdb_id)
);

-- Streaming providers table
CREATE TABLE public.streaming_providers (
    id INTEGER PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    logo_path VARCHAR(255),
    display_priority INTEGER DEFAULT 999
);

-- Streaming availability table
CREATE TABLE public.streaming_availability (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    tmdb_id INTEGER NOT NULL,
    content_type VARCHAR(10) NOT NULL CHECK (content_type IN ('movie', 'tv')),
    provider_id INTEGER REFERENCES public.streaming_providers(id) ON DELETE CASCADE NOT NULL,
    region VARCHAR(5) DEFAULT 'US',
    link TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tmdb_id, content_type, provider_id, region)
);

-- Indexes for better performance
CREATE INDEX idx_content_creator_id ON public.content(creator_id);
CREATE INDEX idx_content_published_at ON public.content(published_at DESC);
CREATE INDEX idx_content_content_type ON public.content(content_type);
CREATE INDEX idx_watchlist_user_id ON public.watchlist(user_id);
CREATE INDEX idx_watchlist_added_at ON public.watchlist(added_at DESC);
CREATE INDEX idx_streaming_availability_tmdb_id ON public.streaming_availability(tmdb_id);
CREATE INDEX idx_user_creators_user_id ON public.user_creators(user_id);
CREATE INDEX idx_creators_platform ON public.creators(platform);

-- Row Level Security (RLS) policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_creators ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.watchlist ENABLE ROW LEVEL SECURITY;

-- Users can only see and edit their own data
CREATE POLICY "Users can view own profile" ON public.users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON public.users FOR INSERT WITH CHECK (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.users FOR UPDATE USING (auth.uid() = id);

-- User preferences policies
CREATE POLICY "Users can view own preferences" ON public.user_preferences FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own preferences" ON public.user_preferences FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own preferences" ON public.user_preferences FOR UPDATE USING (auth.uid() = user_id);

-- User creators policies
CREATE POLICY "Users can view own creators" ON public.user_creators FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own creators" ON public.user_creators FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can delete own creators" ON public.user_creators FOR DELETE USING (auth.uid() = user_id);

-- Watchlist policies
CREATE POLICY "Users can view own watchlist" ON public.watchlist FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own watchlist" ON public.watchlist FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own watchlist" ON public.watchlist FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own watchlist" ON public.watchlist FOR DELETE USING (auth.uid() = user_id);

-- Public read access for reference data
CREATE POLICY "Anyone can view creators" ON public.creators FOR SELECT USING (true);
CREATE POLICY "Anyone can view content" ON public.content FOR SELECT USING (true);
CREATE POLICY "Anyone can view movies" ON public.movies FOR SELECT USING (true);
CREATE POLICY "Anyone can view tv_shows" ON public.tv_shows FOR SELECT USING (true);
CREATE POLICY "Anyone can view streaming_providers" ON public.streaming_providers FOR SELECT USING (true);
CREATE POLICY "Anyone can view streaming_availability" ON public.streaming_availability FOR SELECT USING (true);

-- Functions for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_preferences_updated_at BEFORE UPDATE ON public.user_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_creators_updated_at BEFORE UPDATE ON public.creators FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_content_updated_at BEFORE UPDATE ON public.content FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_movies_updated_at BEFORE UPDATE ON public.movies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tv_shows_updated_at BEFORE UPDATE ON public.tv_shows FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_streaming_availability_updated_at BEFORE UPDATE ON public.streaming_availability FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
