'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/providers/auth-provider'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Film, Users, Settings, LogOut, Bell, Search } from 'lucide-react'

export default function FeedPage() {
  const { user, loading, signOut } = useAuth()
  const router = useRouter()
  const [preferences, setPreferences] = useState<any>(null)
  const [loadingPrefs, setLoadingPrefs] = useState(true)

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth')
    }
  }, [user, loading, router])

  useEffect(() => {
    if (user) {
      fetchPreferences()
    }
  }, [user])

  const fetchPreferences = async () => {
    try {
      const response = await fetch('/api/user/preferences')
      if (response.ok) {
        const data = await response.json()
        setPreferences(data)
      }
    } catch (error) {
      console.error('Error fetching preferences:', error)
    } finally {
      setLoadingPrefs(false)
    }
  }

  const handleSignOut = async () => {
    await signOut()
    router.push('/')
  }

  if (loading || loadingPrefs) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <header className="border-b border-white/20 bg-black/20 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Film className="h-8 w-8 text-purple-400" />
              <h1 className="text-2xl font-bold text-white">SceneSniffer</h1>
            </div>

            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm" className="text-white border-white/30 hover:bg-white/10">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
              <Button variant="outline" size="sm" className="text-white border-white/30 hover:bg-white/10">
                <Bell className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" className="text-white border-white/30 hover:bg-white/10">
                <Settings className="h-4 w-4" />
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleSignOut}
                className="text-white border-white/30 hover:bg-white/10"
              >
                <LogOut className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-white mb-2">
            Welcome back, {user.email?.split('@')[0]}! 👋
          </h2>
          <p className="text-gray-300 text-lg">
            Your personalized cinematic pulse is ready. Here's what's new from your favorite creators.
          </p>
        </div>

        {/* User Stats */}
        {preferences && (
          <div className="grid md:grid-cols-3 gap-6 mb-8">
            <Card className="bg-white/10 border-white/20 text-white">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Film className="h-5 w-5 text-purple-400" />
                  Genres
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold mb-1">
                  {preferences.preferences?.genres?.length || 0}
                </div>
                <p className="text-gray-300 text-sm">Selected genres</p>
                <div className="flex flex-wrap gap-1 mt-2">
                  {preferences.preferences?.genres?.slice(0, 3).map((genre: string) => (
                    <span key={genre} className="px-2 py-1 bg-purple-600/20 text-purple-300 text-xs rounded">
                      {genre}
                    </span>
                  ))}
                  {(preferences.preferences?.genres?.length || 0) > 3 && (
                    <span className="px-2 py-1 bg-gray-600/20 text-gray-300 text-xs rounded">
                      +{(preferences.preferences?.genres?.length || 0) - 3}
                    </span>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/10 border-white/20 text-white">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Users className="h-5 w-5 text-purple-400" />
                  Creators
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold mb-1">
                  {preferences.creators?.length || 0}
                </div>
                <p className="text-gray-300 text-sm">Followed creators</p>
                <div className="space-y-1 mt-2">
                  {preferences.creators?.slice(0, 2).map((creator: any, index: number) => (
                    <div key={index} className="flex items-center gap-2">
                      <span className="text-xs text-gray-400 capitalize">{creator.platform}:</span>
                      <span className="text-white text-xs">{creator.handle}</span>
                    </div>
                  ))}
                  {(preferences.creators?.length || 0) > 2 && (
                    <p className="text-gray-400 text-xs">
                      +{(preferences.creators?.length || 0) - 2} more
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/10 border-white/20 text-white">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <svg className="h-5 w-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  Streaming
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold mb-1">
                  {preferences.preferences?.streaming_services?.length || 0}
                </div>
                <p className="text-gray-300 text-sm">Connected services</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Coming Soon Section */}
        <Card className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 border-purple-400/30">
          <CardHeader>
            <CardTitle className="text-white text-2xl">🚧 Your Feed is Coming Soon!</CardTitle>
            <CardDescription className="text-gray-300 text-lg">
              We're currently building your personalized content feed. Here's what's coming:
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="bg-purple-600 rounded-full p-1">
                    <svg className="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">Personalized Content Feed</h4>
                    <p className="text-gray-300 text-sm">
                      Content from your followed creators, filtered by your favorite genres
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="bg-purple-600 rounded-full p-1">
                    <svg className="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">AI Content Summaries</h4>
                    <p className="text-gray-300 text-sm">
                      Quick, spoiler-free summaries of videos and reviews
                    </p>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="bg-purple-600 rounded-full p-1">
                    <svg className="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">Streaming Availability</h4>
                    <p className="text-gray-300 text-sm">
                      See where to watch on your connected platforms
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="bg-purple-600 rounded-full p-1">
                    <svg className="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">Smart Watchlist</h4>
                    <p className="text-gray-300 text-sm">
                      Save content and get notified when it becomes available
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6 text-center">
              <p className="text-gray-300 mb-4">
                In the meantime, try our search demo to explore movies and TV shows!
              </p>
              <Button 
                onClick={() => router.push('/demo')}
                className="bg-purple-600 hover:bg-purple-700"
              >
                <Search className="mr-2 h-4 w-4" />
                Try Search Demo
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
