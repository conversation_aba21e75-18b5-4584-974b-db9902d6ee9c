import { createServerSupabaseClient, createServiceRoleClient } from '@/lib/supabase-server'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url)
  const code = searchParams.get('code')
  const next = searchParams.get('next') ?? '/onboarding'

  if (code) {
    const supabase = await createServerSupabaseClient()
    const { error } = await supabase.auth.exchangeCodeForSession(code)
    
    if (!error) {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (user) {
        // Create user profile if it doesn't exist
        const { data: existingUser } = await supabase
          .from('users')
          .select('id')
          .eq('id', user.id)
          .single()

        if (!existingUser) {
          // Use service role client to bypass RLS for user creation
          const serviceSupabase = createServiceRoleClient()
          const { error: profileError } = await serviceSupabase
            .from('users')
            .insert({
              id: user.id,
              username: user.email?.split('@')[0] || 'user',
              avatar_url: user.user_metadata?.avatar_url
            })

          if (profileError) {
            console.error('Error creating user profile:', profileError)
            // If user creation fails, redirect to error page
            return NextResponse.redirect(`${origin}/auth/auth-code-error`)
          }

          console.log('User profile created successfully for:', user.email)
        } else {
          console.log('User profile already exists for:', user.email)
        }
      }
      
      return NextResponse.redirect(`${origin}${next}`)
    }
  }

  // Return the user to an error page with instructions
  return NextResponse.redirect(`${origin}/auth/auth-code-error`)
}
