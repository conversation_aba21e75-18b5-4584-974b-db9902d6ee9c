{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/lib/supabase-server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Server-side Supabase client\nexport const createServerSupabaseClient = async () => {\n  const cookieStore = await cookies()\n\n  return createServerClient(supabaseUrl, supabaseAnonKey, {\n    cookies: {\n      getAll() {\n        return cookieStore.getAll()\n      },\n      setAll(cookiesToSet) {\n        try {\n          cookiesToSet.forEach(({ name, value, options }) =>\n            cookieStore.set(name, value, options)\n          )\n        } catch {\n          // The `setAll` method was called from a Server Component.\n          // This can be ignored if you have middleware refreshing\n          // user sessions.\n        }\n      },\n    },\n  })\n}\n\n// Service role client for admin operations\nexport const createServiceRoleClient = () => {\n  return createServerClient(\n    supabaseUrl,\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\n    {\n      cookies: {\n        getAll() { return [] },\n        setAll() {},\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,6BAA6B;IACxC,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AACF;AAGO,MAAM,0BAA0B;IACrC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EACtB,aACA,QAAQ,GAAG,CAAC,yBAAyB,EACrC;QACE,SAAS;YACP;gBAAW,OAAO,EAAE;YAAC;YACrB,WAAU;QACZ;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/app/auth/callback/route.ts"], "sourcesContent": ["import { createServerSupabaseClient } from '@/lib/supabase-server'\nimport { NextRequest, NextResponse } from 'next/server'\n\nexport async function GET(request: NextRequest) {\n  const { searchParams, origin } = new URL(request.url)\n  const code = searchParams.get('code')\n  const next = searchParams.get('next') ?? '/onboarding'\n\n  if (code) {\n    const supabase = await createServerSupabaseClient()\n    const { error } = await supabase.auth.exchangeCodeForSession(code)\n    \n    if (!error) {\n      const { data: { user } } = await supabase.auth.getUser()\n      \n      if (user) {\n        // Create user profile if it doesn't exist\n        const { error: profileError } = await supabase\n          .from('users')\n          .upsert({\n            id: user.id,\n            email: user.email,\n            avatar_url: user.user_metadata?.avatar_url,\n            updated_at: new Date().toISOString()\n          })\n        \n        if (profileError) {\n          console.error('Error creating user profile:', profileError)\n        }\n      }\n      \n      return NextResponse.redirect(`${origin}${next}`)\n    }\n  }\n\n  // Return the user to an error page with instructions\n  return NextResponse.redirect(`${origin}/auth/auth-code-error`)\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IACpD,MAAM,OAAO,aAAa,GAAG,CAAC;IAC9B,MAAM,OAAO,aAAa,GAAG,CAAC,WAAW;IAEzC,IAAI,MAAM;QACR,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,6BAA0B,AAAD;QAChD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,sBAAsB,CAAC;QAE7D,IAAI,CAAC,OAAO;YACV,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YAEtD,IAAI,MAAM;gBACR,0CAA0C;gBAC1C,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,SACL,MAAM,CAAC;oBACN,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,YAAY,KAAK,aAAa,EAAE;oBAChC,YAAY,IAAI,OAAO,WAAW;gBACpC;gBAEF,IAAI,cAAc;oBAChB,QAAQ,KAAK,CAAC,gCAAgC;gBAChD;YACF;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,GAAG,SAAS,MAAM;QACjD;IACF;IAEA,qDAAqD;IACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,GAAG,OAAO,qBAAqB,CAAC;AAC/D", "debugId": null}}]}