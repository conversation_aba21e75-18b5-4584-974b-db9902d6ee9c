{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/lib/supabase-server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Server-side Supabase client\nexport const createServerSupabaseClient = async () => {\n  const cookieStore = await cookies()\n\n  return createServerClient(supabaseUrl, supabaseAnonKey, {\n    cookies: {\n      getAll() {\n        return cookieStore.getAll()\n      },\n      setAll(cookiesToSet) {\n        try {\n          cookiesToSet.forEach(({ name, value, options }) =>\n            cookieStore.set(name, value, options)\n          )\n        } catch {\n          // The `setAll` method was called from a Server Component.\n          // This can be ignored if you have middleware refreshing\n          // user sessions.\n        }\n      },\n    },\n  })\n}\n\n// Service role client for admin operations\nexport const createServiceRoleClient = () => {\n  return createServerClient(\n    supabaseUrl,\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\n    {\n      cookies: {\n        getAll() { return [] },\n        setAll() {},\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,6BAA6B;IACxC,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AACF;AAGO,MAAM,0BAA0B;IACrC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EACtB,aACA,QAAQ,GAAG,CAAC,yBAAyB,EACrC;QACE,SAAS;YACP;gBAAW,OAAO,EAAE;YAAC;YACrB,WAAU;QACZ;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/app/api/debug-schema/route.ts"], "sourcesContent": ["import { createServerSupabaseClient } from '@/lib/supabase-server'\nimport { NextResponse } from 'next/server'\n\nexport async function GET() {\n  try {\n    const supabase = await createServerSupabaseClient()\n    \n    // Check if users table exists and get its structure\n    const { data: usersData, error: usersError } = await supabase\n      .from('users')\n      .select('*')\n      .limit(5)\n    \n    // Check if user_preferences table exists\n    const { data: prefsData, error: prefsError } = await supabase\n      .from('user_preferences')\n      .select('*')\n      .limit(1)\n    \n    return NextResponse.json({\n      success: true,\n      tables: {\n        users: {\n          exists: !usersError,\n          error: usersError?.message,\n          sampleData: usersData\n        },\n        user_preferences: {\n          exists: !prefsError,\n          error: prefsError?.message,\n          sampleData: prefsData\n        }\n      }\n    })\n  } catch (error) {\n    console.error('Schema debug error:', error)\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,6BAA0B,AAAD;QAEhD,oDAAoD;QACpD,MAAM,EAAE,MAAM,SAAS,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,SACL,MAAM,CAAC,KACP,KAAK,CAAC;QAET,yCAAyC;QACzC,MAAM,EAAE,MAAM,SAAS,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,KAAK,CAAC;QAET,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,QAAQ;gBACN,OAAO;oBACL,QAAQ,CAAC;oBACT,OAAO,YAAY;oBACnB,YAAY;gBACd;gBACA,kBAAkB;oBAChB,QAAQ,CAAC;oBACT,OAAO,YAAY;oBACnB,YAAY;gBACd;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}