{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/utils/cn.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/utils/cn\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,kHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/utils/cn\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,kHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,kHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,kHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,kHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,kHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,kHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/components/auth/auth-button.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { createClient } from '@/lib/supabase'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { LogIn, UserPlus, Loader2 } from 'lucide-react'\n\ninterface AuthButtonProps {\n  mode?: 'signin' | 'signup'\n  onSuccess?: () => void\n}\n\nexport function AuthButton({ mode = 'signin', onSuccess }: AuthButtonProps) {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const [message, setMessage] = useState<string | null>(null)\n\n  const supabase = createClient()\n\n  const handleAuth = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError(null)\n    setMessage(null)\n\n    try {\n      if (mode === 'signup') {\n        const { error } = await supabase.auth.signUp({\n          email,\n          password,\n          options: {\n            emailRedirectTo: `${window.location.origin}/auth/callback`\n          }\n        })\n\n        if (error) throw error\n\n        setMessage('Check your email for the confirmation link!')\n      } else {\n        const { error } = await supabase.auth.signInWithPassword({\n          email,\n          password\n        })\n\n        if (error) throw error\n\n        setMessage('Successfully signed in!')\n        onSuccess?.()\n      }\n    } catch (error: any) {\n      setError(error.message)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleGoogleAuth = async () => {\n    setLoading(true)\n    setError(null)\n\n    try {\n      const { error } = await supabase.auth.signInWithOAuth({\n        provider: 'google',\n        options: {\n          redirectTo: `${window.location.origin}/auth/callback`\n        }\n      })\n\n      if (error) throw error\n    } catch (error: any) {\n      setError(error.message)\n      setLoading(false)\n    }\n  }\n\n  return (\n    <Card className=\"w-full max-w-md\">\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          {mode === 'signup' ? <UserPlus className=\"h-5 w-5\" /> : <LogIn className=\"h-5 w-5\" />}\n          {mode === 'signup' ? 'Create Account' : 'Sign In'}\n        </CardTitle>\n        <CardDescription>\n          {mode === 'signup' \n            ? 'Join SceneSniffer to get personalized movie and TV recommendations'\n            : 'Welcome back to SceneSniffer'\n          }\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        <form onSubmit={handleAuth} className=\"space-y-4\">\n          <div>\n            <label htmlFor=\"email\" className=\"block text-sm font-medium mb-1\">\n              Email\n            </label>\n            <input\n              id=\"email\"\n              type=\"email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              required\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500\"\n              placeholder=\"<EMAIL>\"\n            />\n          </div>\n          \n          <div>\n            <label htmlFor=\"password\" className=\"block text-sm font-medium mb-1\">\n              Password\n            </label>\n            <input\n              id=\"password\"\n              type=\"password\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              required\n              minLength={6}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500\"\n              placeholder=\"••••••••\"\n            />\n          </div>\n\n          <Button \n            type=\"submit\" \n            disabled={loading || !email || !password}\n            className=\"w-full bg-purple-600 hover:bg-purple-700\"\n          >\n            {loading ? (\n              <>\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                {mode === 'signup' ? 'Creating Account...' : 'Signing In...'}\n              </>\n            ) : (\n              mode === 'signup' ? 'Create Account' : 'Sign In'\n            )}\n          </Button>\n        </form>\n\n        <div className=\"relative\">\n          <div className=\"absolute inset-0 flex items-center\">\n            <span className=\"w-full border-t\" />\n          </div>\n          <div className=\"relative flex justify-center text-xs uppercase\">\n            <span className=\"bg-background px-2 text-muted-foreground\">Or continue with</span>\n          </div>\n        </div>\n\n        <Button\n          type=\"button\"\n          variant=\"outline\"\n          onClick={handleGoogleAuth}\n          disabled={loading}\n          className=\"w-full\"\n        >\n          {loading ? (\n            <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n          ) : (\n            <svg className=\"mr-2 h-4 w-4\" viewBox=\"0 0 24 24\">\n              <path\n                fill=\"currentColor\"\n                d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n              />\n              <path\n                fill=\"currentColor\"\n                d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n              />\n              <path\n                fill=\"currentColor\"\n                d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n              />\n              <path\n                fill=\"currentColor\"\n                d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n              />\n            </svg>\n          )}\n          Continue with Google\n        </Button>\n\n        {error && (\n          <div className=\"p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md\">\n            {error}\n          </div>\n        )}\n\n        {message && (\n          <div className=\"p-3 text-sm text-green-600 bg-green-50 border border-green-200 rounded-md\">\n            {message}\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AANA;;;;;;;AAaO,SAAS,WAAW,EAAE,OAAO,QAAQ,EAAE,SAAS,EAAmB;IACxE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtD,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,aAAa,OAAO;QACxB,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,IAAI,SAAS,UAAU;gBACrB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;oBAC3C;oBACA;oBACA,SAAS;wBACP,iBAAiB,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC;oBAC5D;gBACF;gBAEA,IAAI,OAAO,MAAM;gBAEjB,WAAW;YACb,OAAO;gBACL,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;oBACvD;oBACA;gBACF;gBAEA,IAAI,OAAO,MAAM;gBAEjB,WAAW;gBACX;YACF;QACF,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO;QACxB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,eAAe,CAAC;gBACpD,UAAU;gBACV,SAAS;oBACP,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC;gBACvD;YACF;YAEA,IAAI,OAAO,MAAM;QACnB,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO;YACtB,WAAW;QACb;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;4BAClB,SAAS,yBAAW,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;qDAAe,8OAAC,wMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BACxE,SAAS,WAAW,mBAAmB;;;;;;;kCAE1C,8OAAC,gIAAA,CAAA,kBAAe;kCACb,SAAS,WACN,uEACA;;;;;;;;;;;;0BAIR,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAK,UAAU;wBAAY,WAAU;;0CACpC,8OAAC;;kDACC,8OAAC;wCAAM,SAAQ;wCAAQ,WAAU;kDAAiC;;;;;;kDAGlE,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,QAAQ;wCACR,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,8OAAC;;kDACC,8OAAC;wCAAM,SAAQ;wCAAW,WAAU;kDAAiC;;;;;;kDAGrE,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC3C,QAAQ;wCACR,WAAW;wCACX,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,UAAU,WAAW,CAAC,SAAS,CAAC;gCAChC,WAAU;0CAET,wBACC;;sDACE,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAClB,SAAS,WAAW,wBAAwB;;mDAG/C,SAAS,WAAW,mBAAmB;;;;;;;;;;;;kCAK7C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;;;;;;;;;;0CAElB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;;;;;;kCAI/D,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS;wBACT,UAAU;wBACV,WAAU;;4BAET,wBACC,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;qDAEnB,8OAAC;gCAAI,WAAU;gCAAe,SAAQ;;kDACpC,8OAAC;wCACC,MAAK;wCACL,GAAE;;;;;;kDAEJ,8OAAC;wCACC,MAAK;wCACL,GAAE;;;;;;kDAEJ,8OAAC;wCACC,MAAK;wCACL,GAAE;;;;;;kDAEJ,8OAAC;wCACC,MAAK;wCACL,GAAE;;;;;;;;;;;;4BAGN;;;;;;;oBAIH,uBACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;oBAIJ,yBACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/app/auth/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/components/providers/auth-provider'\nimport { AuthButton } from '@/components/auth/auth-button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Film, ArrowLeft } from 'lucide-react'\nimport Link from 'next/link'\n\nexport default function AuthPage() {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n  const [mode, setMode] = useState<'signin' | 'signup'>('signin')\n\n  useEffect(() => {\n    if (!loading && user) {\n      router.push('/onboarding')\n    }\n  }, [user, loading, router])\n\n  const handleAuthSuccess = () => {\n    router.push('/onboarding')\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-white\"></div>\n      </div>\n    )\n  }\n\n  if (user) {\n    return null\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-8\">\n          <Link href=\"/\">\n            <Button variant=\"outline\" className=\"text-white border-white/50 hover:bg-white hover:text-black bg-transparent\">\n              <ArrowLeft className=\"mr-2 h-4 w-4\" />\n              Back to Home\n            </Button>\n          </Link>\n          \n          <div className=\"flex items-center gap-2\">\n            <Film className=\"h-8 w-8 text-purple-400\" />\n            <span className=\"text-2xl font-bold text-white\">SceneSniffer</span>\n          </div>\n        </div>\n\n        <div className=\"max-w-6xl mx-auto grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Side - Marketing Content */}\n          <div className=\"space-y-8\">\n            <div>\n              <h1 className=\"text-4xl lg:text-5xl font-bold text-white mb-4\">\n                Your Cinematic Pulse Awaits\n              </h1>\n              <p className=\"text-xl text-gray-300 mb-6\">\n                Join thousands of movie and TV enthusiasts who trust SceneSniffer for personalized content discovery.\n              </p>\n            </div>\n\n            <div className=\"space-y-6\">\n              <div className=\"flex items-start gap-4\">\n                <div className=\"bg-purple-600 rounded-full p-2\">\n                  <Film className=\"h-6 w-6 text-white\" />\n                </div>\n                <div>\n                  <h3 className=\"text-lg font-semibold text-white mb-1\">Creator-First Discovery</h3>\n                  <p className=\"text-gray-300\">\n                    Follow your favorite YouTube, Instagram, and Twitter creators. Get recommendations from voices you trust.\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start gap-4\">\n                <div className=\"bg-purple-600 rounded-full p-2\">\n                  <svg className=\"h-6 w-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h3 className=\"text-lg font-semibold text-white mb-1\">AI-Powered Insights</h3>\n                  <p className=\"text-gray-300\">\n                    Smart summaries, automatic content categorization, and spoiler-free recommendations powered by AI.\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start gap-4\">\n                <div className=\"bg-purple-600 rounded-full p-2\">\n                  <svg className=\"h-6 w-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h3 className=\"text-lg font-semibold text-white mb-1\">Streaming Intelligence</h3>\n                  <p className=\"text-gray-300\">\n                    Real-time \"Where to Watch\" information across all major platforms. Never miss when content becomes available.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white/5 border border-white/20 rounded-lg p-6\">\n              <div className=\"flex items-center gap-3 mb-3\">\n                <div className=\"flex -space-x-2\">\n                  <div className=\"w-8 h-8 bg-purple-500 rounded-full border-2 border-white\"></div>\n                  <div className=\"w-8 h-8 bg-blue-500 rounded-full border-2 border-white\"></div>\n                  <div className=\"w-8 h-8 bg-green-500 rounded-full border-2 border-white\"></div>\n                </div>\n                <span className=\"text-white font-medium\">Join 10,000+ users</span>\n              </div>\n              <p className=\"text-gray-300 text-sm\">\n                \"SceneSniffer has completely changed how I discover new shows. The creator recommendations are spot-on!\" \n                <span className=\"text-purple-400\">- Sarah M.</span>\n              </p>\n            </div>\n          </div>\n\n          {/* Right Side - Auth Form */}\n          <div className=\"flex flex-col items-center\">\n            <div className=\"w-full max-w-md\">\n              <div className=\"text-center mb-6\">\n                <div className=\"flex justify-center gap-2 mb-4\">\n                  <Button\n                    variant={mode === 'signin' ? 'default' : 'outline'}\n                    onClick={() => setMode('signin')}\n                    className={mode === 'signin' ? 'bg-purple-600 hover:bg-purple-700 text-white' : 'border-white/50 text-white hover:bg-white/10 bg-transparent'}\n                  >\n                    Sign In\n                  </Button>\n                  <Button\n                    variant={mode === 'signup' ? 'default' : 'outline'}\n                    onClick={() => setMode('signup')}\n                    className={mode === 'signup' ? 'bg-purple-600 hover:bg-purple-700 text-white' : 'border-white/50 text-white hover:bg-white/10 bg-transparent'}\n                  >\n                    Sign Up\n                  </Button>\n                </div>\n              </div>\n\n              <AuthButton mode={mode} onSuccess={handleAuthSuccess} />\n\n              <div className=\"mt-6 text-center\">\n                <p className=\"text-gray-400 text-sm\">\n                  By continuing, you agree to our{' '}\n                  <Link href=\"/terms\" className=\"text-purple-400 hover:underline\">\n                    Terms of Service\n                  </Link>{' '}\n                  and{' '}\n                  <Link href=\"/privacy\" className=\"text-purple-400 hover:underline\">\n                    Privacy Policy\n                  </Link>\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AACA;AATA;;;;;;;;;AAWe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,MAAM;YACpB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,MAAM,oBAAoB;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,MAAM;QACR,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,WAAU;;kDAClC,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAK1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;8BAIpD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAiD;;;;;;sDAG/D,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAK5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAwC;;;;;;sEACtD,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAMjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAqB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC5E,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAwC;;;;;;sEACtD,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAMjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAqB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC5E,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAwC;;;;;;sEACtD,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;8CAOnC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,8OAAC;oDAAK,WAAU;8DAAyB;;;;;;;;;;;;sDAE3C,8OAAC;4CAAE,WAAU;;gDAAwB;8DAEnC,8OAAC;oDAAK,WAAU;8DAAkB;;;;;;;;;;;;;;;;;;;;;;;;sCAMxC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,SAAS,WAAW,YAAY;oDACzC,SAAS,IAAM,QAAQ;oDACvB,WAAW,SAAS,WAAW,iDAAiD;8DACjF;;;;;;8DAGD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,SAAS,WAAW,YAAY;oDACzC,SAAS,IAAM,QAAQ;oDACvB,WAAW,SAAS,WAAW,iDAAiD;8DACjF;;;;;;;;;;;;;;;;;kDAML,8OAAC,4IAAA,CAAA,aAAU;wCAAC,MAAM;wCAAM,WAAW;;;;;;kDAEnC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;gDAAwB;gDACH;8DAChC,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;8DAAkC;;;;;;gDAExD;gDAAI;gDACR;8DACJ,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;8DAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWpF", "debugId": null}}]}