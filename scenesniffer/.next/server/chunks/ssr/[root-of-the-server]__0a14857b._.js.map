{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/utils/cn.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/utils/cn\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,kHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/utils/cn\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,kHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,kHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,kHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,kHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,kHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,kHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/components/ui/progress.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/utils/cn\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-2 w-full overflow-hidden rounded-full bg-primary/20\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,yBAAW,qMAAA,CAAA,aAAgB,CAG/B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,kHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/utils/constants.ts"], "sourcesContent": ["// Genre options for onboarding\nexport const GENRES = [\n  'Action',\n  'Adventure',\n  'Animation',\n  'Comedy',\n  'Crime',\n  'Documentary',\n  'Drama',\n  'Family',\n  'Fantasy',\n  'History',\n  'Horror',\n  'Music',\n  'Mystery',\n  'Romance',\n  'Science Fiction',\n  'Thriller',\n  'War',\n  'Western',\n  'K-Drama',\n  'Anime',\n  'True Crime',\n  'Superhero',\n  'Psychological Thriller',\n  'Dark Comedy',\n  'Indie',\n  'Foreign',\n  'Biographical',\n  'Sports'\n] as const\n\n// Streaming service options\nexport const STREAMING_SERVICES = [\n  { id: 'netflix', name: 'Netflix', logo: '/logos/netflix.png' },\n  { id: 'amazon-prime', name: 'Amazon Prime Video', logo: '/logos/prime.png' },\n  { id: 'disney-plus', name: 'Disney+', logo: '/logos/disney.png' },\n  { id: 'hulu', name: '<PERSON><PERSON>', logo: '/logos/hulu.png' },\n  { id: 'hbo-max', name: '<PERSON> (HBO)', logo: '/logos/hbo.png' },\n  { id: 'apple-tv', name: 'Apple TV+', logo: '/logos/apple.png' },\n  { id: 'paramount-plus', name: 'Paramount+', logo: '/logos/paramount.png' },\n  { id: 'peacock', name: 'Peacock', logo: '/logos/peacock.png' },\n  { id: 'crunchyroll', name: 'Crunchyroll', logo: '/logos/crunchyroll.png' },\n  { id: 'funimation', name: 'Funimation', logo: '/logos/funimation.png' },\n  { id: 'youtube-tv', name: 'YouTube TV', logo: '/logos/youtube-tv.png' },\n  { id: 'starz', name: 'Starz', logo: '/logos/starz.png' },\n  { id: 'showtime', name: 'Showtime', logo: '/logos/showtime.png' }\n] as const\n\n// Platform options for creators\nexport const PLATFORMS = [\n  { id: 'youtube', name: 'YouTube', icon: '📺' },\n  { id: 'instagram', name: 'Instagram', icon: '📷' },\n  { id: 'twitter', name: 'Twitter/X', icon: '🐦' },\n  { id: 'tiktok', name: 'TikTok', icon: '🎵' }\n] as const\n\n// Content type classifications\nexport const CONTENT_TYPES = [\n  { id: 'review', name: 'Review', color: 'bg-blue-100 text-blue-800' },\n  { id: 'theory', name: 'Theory', color: 'bg-purple-100 text-purple-800' },\n  { id: 'news', name: 'News', color: 'bg-green-100 text-green-800' },\n  { id: 'spoiler-free', name: 'Spoiler-Free', color: 'bg-yellow-100 text-yellow-800' },\n  { id: 'breakdown', name: 'Breakdown', color: 'bg-red-100 text-red-800' },\n  { id: 'recommendation', name: 'Recommendation', color: 'bg-indigo-100 text-indigo-800' }\n] as const\n\n// TMDb configuration\nexport const TMDB_BASE_URL = 'https://api.themoviedb.org/3'\nexport const TMDB_IMAGE_BASE_URL = 'https://image.tmdb.org/t/p'\n\n// YouTube API configuration\nexport const YOUTUBE_API_BASE_URL = 'https://www.googleapis.com/youtube/v3'\n\n// App configuration\nexport const APP_NAME = 'SceneSniffer'\nexport const APP_DESCRIPTION = 'Your personalized movie and TV content intelligence platform'\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;;;;;AACxB,MAAM,SAAS;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,qBAAqB;IAChC;QAAE,IAAI;QAAW,MAAM;QAAW,MAAM;IAAqB;IAC7D;QAAE,IAAI;QAAgB,MAAM;QAAsB,MAAM;IAAmB;IAC3E;QAAE,IAAI;QAAe,MAAM;QAAW,MAAM;IAAoB;IAChE;QAAE,IAAI;QAAQ,MAAM;QAAQ,MAAM;IAAkB;IACpD;QAAE,IAAI;QAAW,MAAM;QAAa,MAAM;IAAiB;IAC3D;QAAE,IAAI;QAAY,MAAM;QAAa,MAAM;IAAmB;IAC9D;QAAE,IAAI;QAAkB,MAAM;QAAc,MAAM;IAAuB;IACzE;QAAE,IAAI;QAAW,MAAM;QAAW,MAAM;IAAqB;IAC7D;QAAE,IAAI;QAAe,MAAM;QAAe,MAAM;IAAyB;IACzE;QAAE,IAAI;QAAc,MAAM;QAAc,MAAM;IAAwB;IACtE;QAAE,IAAI;QAAc,MAAM;QAAc,MAAM;IAAwB;IACtE;QAAE,IAAI;QAAS,MAAM;QAAS,MAAM;IAAmB;IACvD;QAAE,IAAI;QAAY,MAAM;QAAY,MAAM;IAAsB;CACjE;AAGM,MAAM,YAAY;IACvB;QAAE,IAAI;QAAW,MAAM;QAAW,MAAM;IAAK;IAC7C;QAAE,IAAI;QAAa,MAAM;QAAa,MAAM;IAAK;IACjD;QAAE,IAAI;QAAW,MAAM;QAAa,MAAM;IAAK;IAC/C;QAAE,IAAI;QAAU,MAAM;QAAU,MAAM;IAAK;CAC5C;AAGM,MAAM,gBAAgB;IAC3B;QAAE,IAAI;QAAU,MAAM;QAAU,OAAO;IAA4B;IACnE;QAAE,IAAI;QAAU,MAAM;QAAU,OAAO;IAAgC;IACvE;QAAE,IAAI;QAAQ,MAAM;QAAQ,OAAO;IAA8B;IACjE;QAAE,IAAI;QAAgB,MAAM;QAAgB,OAAO;IAAgC;IACnF;QAAE,IAAI;QAAa,MAAM;QAAa,OAAO;IAA0B;IACvE;QAAE,IAAI;QAAkB,MAAM;QAAkB,OAAO;IAAgC;CACxF;AAGM,MAAM,gBAAgB;AACtB,MAAM,sBAAsB;AAG5B,MAAM,uBAAuB;AAG7B,MAAM,WAAW;AACjB,MAAM,kBAAkB", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/components/onboarding/genre-selection.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { GENRES } from '@/utils/constants'\nimport { Check } from 'lucide-react'\n\ninterface GenreSelectionProps {\n  selectedGenres: string[]\n  onGenresChange: (genres: string[]) => void\n}\n\nexport function GenreSelection({ selectedGenres, onGenresChange }: GenreSelectionProps) {\n  const toggleGenre = (genre: string) => {\n    if (selectedGenres.includes(genre)) {\n      onGenresChange(selectedGenres.filter(g => g !== genre))\n    } else {\n      onGenresChange([...selectedGenres, genre])\n    }\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <p className=\"text-gray-300 mb-4\">\n          Select at least 3 genres you enjoy. This helps us curate content that matches your taste.\n        </p>\n        <p className=\"text-sm text-gray-400\">\n          Selected: {selectedGenres.length} {selectedGenres.length === 1 ? 'genre' : 'genres'}\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3\">\n        {GENRES.map((genre) => {\n          const isSelected = selectedGenres.includes(genre)\n          return (\n            <Button\n              key={genre}\n              variant={isSelected ? \"default\" : \"outline\"}\n              onClick={() => toggleGenre(genre)}\n              className={`\n                relative h-12 text-sm transition-all duration-200\n                ${isSelected \n                  ? 'bg-purple-600 hover:bg-purple-700 text-white border-purple-600' \n                  : 'bg-transparent border-white/30 text-white hover:bg-white/10 hover:border-white/50'\n                }\n              `}\n            >\n              {isSelected && (\n                <Check className=\"absolute left-2 h-4 w-4\" />\n              )}\n              <span className={isSelected ? 'ml-6' : ''}>\n                {genre}\n              </span>\n            </Button>\n          )\n        })}\n      </div>\n\n      {selectedGenres.length > 0 && (\n        <div className=\"mt-6 p-4 bg-white/5 rounded-lg border border-white/10\">\n          <h4 className=\"font-semibold text-white mb-2\">Your Selected Genres:</h4>\n          <div className=\"flex flex-wrap gap-2\">\n            {selectedGenres.map((genre) => (\n              <span\n                key={genre}\n                className=\"px-3 py-1 bg-purple-600 text-white text-sm rounded-full\"\n              >\n                {genre}\n              </span>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {selectedGenres.length < 3 && (\n        <div className=\"text-center\">\n          <p className=\"text-yellow-400 text-sm\">\n            💡 Select at least 3 genres to get the best recommendations\n          </p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAYO,SAAS,eAAe,EAAE,cAAc,EAAE,cAAc,EAAuB;IACpF,MAAM,cAAc,CAAC;QACnB,IAAI,eAAe,QAAQ,CAAC,QAAQ;YAClC,eAAe,eAAe,MAAM,CAAC,CAAA,IAAK,MAAM;QAClD,OAAO;YACL,eAAe;mBAAI;gBAAgB;aAAM;QAC3C;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC;wBAAE,WAAU;;4BAAwB;4BACxB,eAAe,MAAM;4BAAC;4BAAE,eAAe,MAAM,KAAK,IAAI,UAAU;;;;;;;;;;;;;0BAI/E,8OAAC;gBAAI,WAAU;0BACZ,yHAAA,CAAA,SAAM,CAAC,GAAG,CAAC,CAAC;oBACX,MAAM,aAAa,eAAe,QAAQ,CAAC;oBAC3C,qBACE,8OAAC,kIAAA,CAAA,SAAM;wBAEL,SAAS,aAAa,YAAY;wBAClC,SAAS,IAAM,YAAY;wBAC3B,WAAW,CAAC;;gBAEV,EAAE,aACE,mEACA,oFACH;cACH,CAAC;;4BAEA,4BACC,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CAEnB,8OAAC;gCAAK,WAAW,aAAa,SAAS;0CACpC;;;;;;;uBAfE;;;;;gBAmBX;;;;;;YAGD,eAAe,MAAM,GAAG,mBACvB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAgC;;;;;;kCAC9C,8OAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC;gCAEC,WAAU;0CAET;+BAHI;;;;;;;;;;;;;;;;YAUd,eAAe,MAAM,GAAG,mBACvB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAA0B;;;;;;;;;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 541, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/components/onboarding/creator-selection.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { PLATFORMS } from '@/utils/constants'\nimport { Plus, X, Search, Loader2 } from 'lucide-react'\n\ninterface Creator {\n  platform: string\n  handle: string\n}\n\ninterface CreatorSelectionProps {\n  selectedCreators: Creator[]\n  onCreatorsChange: (creators: Creator[]) => void\n}\n\nexport function CreatorSelection({ selectedCreators, onCreatorsChange }: CreatorSelectionProps) {\n  const [selectedPlatform, setSelectedPlatform] = useState('youtube')\n  const [handle, setHandle] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n\n  const addCreator = async () => {\n    if (!handle.trim()) return\n\n    setLoading(true)\n    setError(null)\n\n    try {\n      // Clean up the handle\n      let cleanHandle = handle.trim()\n      if (selectedPlatform === 'youtube' && !cleanHandle.startsWith('@')) {\n        cleanHandle = `@${cleanHandle}`\n      }\n      if (selectedPlatform === 'twitter' && !cleanHandle.startsWith('@')) {\n        cleanHandle = `@${cleanHandle}`\n      }\n      if (selectedPlatform === 'instagram' && cleanHandle.startsWith('@')) {\n        cleanHandle = cleanHandle.substring(1)\n      }\n\n      // Check if creator already exists\n      const exists = selectedCreators.some(\n        c => c.platform === selectedPlatform && c.handle.toLowerCase() === cleanHandle.toLowerCase()\n      )\n\n      if (exists) {\n        setError('You\\'ve already added this creator')\n        return\n      }\n\n      // Add the creator\n      const newCreator = {\n        platform: selectedPlatform,\n        handle: cleanHandle\n      }\n\n      onCreatorsChange([...selectedCreators, newCreator])\n      setHandle('')\n      setError(null)\n    } catch (error) {\n      setError('Failed to add creator. Please try again.')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const removeCreator = (index: number) => {\n    const newCreators = selectedCreators.filter((_, i) => i !== index)\n    onCreatorsChange(newCreators)\n  }\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      addCreator()\n    }\n  }\n\n  const getPlaceholder = () => {\n    switch (selectedPlatform) {\n      case 'youtube':\n        return '@mkbhd or MKBHD'\n      case 'instagram':\n        return 'username (without @)'\n      case 'twitter':\n        return '@username'\n      case 'tiktok':\n        return '@username'\n      default:\n        return 'Enter handle'\n    }\n  }\n\n  const getPlatformIcon = (platform: string) => {\n    const platformData = PLATFORMS.find(p => p.id === platform)\n    return platformData?.icon || '📺'\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <p className=\"text-gray-300 mb-4\">\n          Add your favorite movie and TV content creators. We'll aggregate their content to create your personalized feed.\n        </p>\n        <p className=\"text-sm text-gray-400\">\n          Added: {selectedCreators.length} {selectedCreators.length === 1 ? 'creator' : 'creators'}\n        </p>\n      </div>\n\n      {/* Add Creator Form */}\n      <Card className=\"bg-white/5 border-white/20\">\n        <CardHeader>\n          <CardTitle className=\"text-white text-lg\">Add a Creator</CardTitle>\n          <CardDescription className=\"text-gray-300\">\n            Choose a platform and enter the creator's handle\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          {/* Platform Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-white mb-2\">Platform</label>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-2\">\n              {PLATFORMS.map((platform) => (\n                <Button\n                  key={platform.id}\n                  variant={selectedPlatform === platform.id ? \"default\" : \"outline\"}\n                  onClick={() => setSelectedPlatform(platform.id)}\n                  className={`\n                    ${selectedPlatform === platform.id\n                      ? 'bg-purple-600 hover:bg-purple-700 text-white'\n                      : 'bg-transparent border-white/30 text-white hover:bg-white/10'\n                    }\n                  `}\n                >\n                  <span className=\"mr-2\">{platform.icon}</span>\n                  {platform.name}\n                </Button>\n              ))}\n            </div>\n          </div>\n\n          {/* Handle Input */}\n          <div>\n            <label className=\"block text-sm font-medium text-white mb-2\">Creator Handle</label>\n            <div className=\"flex gap-2\">\n              <input\n                type=\"text\"\n                value={handle}\n                onChange={(e) => setHandle(e.target.value)}\n                onKeyPress={handleKeyPress}\n                placeholder={getPlaceholder()}\n                className=\"flex-1 px-3 py-2 bg-white/10 border border-white/30 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500\"\n              />\n              <Button\n                onClick={addCreator}\n                disabled={!handle.trim() || loading}\n                className=\"bg-purple-600 hover:bg-purple-700\"\n              >\n                {loading ? (\n                  <Loader2 className=\"h-4 w-4 animate-spin\" />\n                ) : (\n                  <Plus className=\"h-4 w-4\" />\n                )}\n              </Button>\n            </div>\n            {error && (\n              <p className=\"text-red-400 text-sm mt-1\">{error}</p>\n            )}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Selected Creators */}\n      {selectedCreators.length > 0 && (\n        <div>\n          <h4 className=\"font-semibold text-white mb-3\">Your Selected Creators:</h4>\n          <div className=\"grid gap-3\">\n            {selectedCreators.map((creator, index) => (\n              <div\n                key={`${creator.platform}-${creator.handle}`}\n                className=\"flex items-center justify-between p-3 bg-white/5 rounded-lg border border-white/10\"\n              >\n                <div className=\"flex items-center gap-3\">\n                  <span className=\"text-2xl\">{getPlatformIcon(creator.platform)}</span>\n                  <div>\n                    <p className=\"text-white font-medium\">{creator.handle}</p>\n                    <p className=\"text-gray-400 text-sm capitalize\">{creator.platform}</p>\n                  </div>\n                </div>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => removeCreator(index)}\n                  className=\"border-red-400 text-red-400 hover:bg-red-400 hover:text-white\"\n                >\n                  <X className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {selectedCreators.length === 0 && (\n        <div className=\"text-center py-8\">\n          <Search className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n          <p className=\"text-gray-400\">\n            No creators added yet. Add some creators to get personalized recommendations!\n          </p>\n        </div>\n      )}\n\n      {selectedCreators.length > 0 && selectedCreators.length < 3 && (\n        <div className=\"text-center\">\n          <p className=\"text-yellow-400 text-sm\">\n            💡 Add at least 3 creators for the best personalized experience\n          </p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;;AAkBO,SAAS,iBAAiB,EAAE,gBAAgB,EAAE,gBAAgB,EAAyB;IAC5F,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,aAAa;QACjB,IAAI,CAAC,OAAO,IAAI,IAAI;QAEpB,WAAW;QACX,SAAS;QAET,IAAI;YACF,sBAAsB;YACtB,IAAI,cAAc,OAAO,IAAI;YAC7B,IAAI,qBAAqB,aAAa,CAAC,YAAY,UAAU,CAAC,MAAM;gBAClE,cAAc,CAAC,CAAC,EAAE,aAAa;YACjC;YACA,IAAI,qBAAqB,aAAa,CAAC,YAAY,UAAU,CAAC,MAAM;gBAClE,cAAc,CAAC,CAAC,EAAE,aAAa;YACjC;YACA,IAAI,qBAAqB,eAAe,YAAY,UAAU,CAAC,MAAM;gBACnE,cAAc,YAAY,SAAS,CAAC;YACtC;YAEA,kCAAkC;YAClC,MAAM,SAAS,iBAAiB,IAAI,CAClC,CAAA,IAAK,EAAE,QAAQ,KAAK,oBAAoB,EAAE,MAAM,CAAC,WAAW,OAAO,YAAY,WAAW;YAG5F,IAAI,QAAQ;gBACV,SAAS;gBACT;YACF;YAEA,kBAAkB;YAClB,MAAM,aAAa;gBACjB,UAAU;gBACV,QAAQ;YACV;YAEA,iBAAiB;mBAAI;gBAAkB;aAAW;YAClD,UAAU;YACV,SAAS;QACX,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,cAAc,iBAAiB,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAC5D,iBAAiB;IACnB;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,eAAe,yHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAClD,OAAO,cAAc,QAAQ;IAC/B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC;wBAAE,WAAU;;4BAAwB;4BAC3B,iBAAiB,MAAM;4BAAC;4BAAE,iBAAiB,MAAM,KAAK,IAAI,YAAY;;;;;;;;;;;;;0BAKlF,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAqB;;;;;;0CAC1C,8OAAC,gIAAA,CAAA,kBAAe;gCAAC,WAAU;0CAAgB;;;;;;;;;;;;kCAI7C,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA4C;;;;;;kDAC7D,8OAAC;wCAAI,WAAU;kDACZ,yHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,yBACd,8OAAC,kIAAA,CAAA,SAAM;gDAEL,SAAS,qBAAqB,SAAS,EAAE,GAAG,YAAY;gDACxD,SAAS,IAAM,oBAAoB,SAAS,EAAE;gDAC9C,WAAW,CAAC;oBACV,EAAE,qBAAqB,SAAS,EAAE,GAC9B,iDACA,8DACH;kBACH,CAAC;;kEAED,8OAAC;wDAAK,WAAU;kEAAQ,SAAS,IAAI;;;;;;oDACpC,SAAS,IAAI;;+CAXT,SAAS,EAAE;;;;;;;;;;;;;;;;0CAkBxB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA4C;;;;;;kDAC7D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gDACzC,YAAY;gDACZ,aAAa;gDACb,WAAU;;;;;;0DAEZ,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,UAAU,CAAC,OAAO,IAAI,MAAM;gDAC5B,WAAU;0DAET,wBACC,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;yEAEnB,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;oCAIrB,uBACC,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;;YAOjD,iBAAiB,MAAM,GAAG,mBACzB,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAgC;;;;;;kCAC9C,8OAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAY,gBAAgB,QAAQ,QAAQ;;;;;;0DAC5D,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAA0B,QAAQ,MAAM;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEAAoC,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;kDAGrE,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,cAAc;wCAC7B,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;+BAhBV,GAAG,QAAQ,QAAQ,CAAC,CAAC,EAAE,QAAQ,MAAM,EAAE;;;;;;;;;;;;;;;;YAwBrD,iBAAiB,MAAM,KAAK,mBAC3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;YAMhC,iBAAiB,MAAM,GAAG,KAAK,iBAAiB,MAAM,GAAG,mBACxD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAA0B;;;;;;;;;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/components/onboarding/streaming-selection.tsx"], "sourcesContent": ["'use client'\n\nimport { Button } from '@/components/ui/button'\nimport { STREAMING_SERVICES } from '@/utils/constants'\nimport { Check } from 'lucide-react'\n\ninterface StreamingSelectionProps {\n  selectedServices: string[]\n  onServicesChange: (services: string[]) => void\n}\n\nexport function StreamingSelection({ selectedServices, onServicesChange }: StreamingSelectionProps) {\n  const toggleService = (serviceId: string) => {\n    if (selectedServices.includes(serviceId)) {\n      onServicesChange(selectedServices.filter(s => s !== serviceId))\n    } else {\n      onServicesChange([...selectedServices, serviceId])\n    }\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <p className=\"text-gray-300 mb-4\">\n          Select the streaming services you have access to. This helps us show you where to watch recommended content.\n        </p>\n        <p className=\"text-sm text-gray-400\">\n          Selected: {selectedServices.length} {selectedServices.length === 1 ? 'service' : 'services'}\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n        {STREAMING_SERVICES.map((service) => {\n          const isSelected = selectedServices.includes(service.id)\n          return (\n            <Button\n              key={service.id}\n              variant=\"outline\"\n              onClick={() => toggleService(service.id)}\n              className={`\n                relative h-16 p-4 transition-all duration-200 flex items-center justify-start gap-3\n                ${isSelected \n                  ? 'bg-purple-600 hover:bg-purple-700 text-white border-purple-600' \n                  : 'bg-white/5 border-white/20 text-white hover:bg-white/10 hover:border-white/40'\n                }\n              `}\n            >\n              {isSelected && (\n                <Check className=\"absolute top-2 right-2 h-4 w-4\" />\n              )}\n              \n              {/* Service Logo Placeholder */}\n              <div className=\"w-8 h-8 bg-white/20 rounded flex items-center justify-center text-xs font-bold\">\n                {service.name.charAt(0)}\n              </div>\n              \n              <div className=\"flex-1 text-left\">\n                <p className=\"font-medium\">{service.name}</p>\n              </div>\n            </Button>\n          )\n        })}\n      </div>\n\n      {selectedServices.length > 0 && (\n        <div className=\"mt-6 p-4 bg-white/5 rounded-lg border border-white/10\">\n          <h4 className=\"font-semibold text-white mb-2\">Your Streaming Services:</h4>\n          <div className=\"flex flex-wrap gap-2\">\n            {selectedServices.map((serviceId) => {\n              const service = STREAMING_SERVICES.find(s => s.id === serviceId)\n              return (\n                <span\n                  key={serviceId}\n                  className=\"px-3 py-1 bg-purple-600 text-white text-sm rounded-full\"\n                >\n                  {service?.name}\n                </span>\n              )\n            })}\n          </div>\n        </div>\n      )}\n\n      <div className=\"bg-blue-500/10 border border-blue-500/20 rounded-lg p-4\">\n        <div className=\"flex items-start gap-3\">\n          <div className=\"text-blue-400 text-xl\">💡</div>\n          <div>\n            <h4 className=\"text-blue-400 font-medium mb-1\">Pro Tip</h4>\n            <p className=\"text-blue-300 text-sm\">\n              Don't worry if you don't see your service listed. You can always update your preferences later, \n              and we'll show availability across all major platforms.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {selectedServices.length === 0 && (\n        <div className=\"text-center\">\n          <p className=\"text-yellow-400 text-sm\">\n            💡 Select at least one streaming service to see personalized availability\n          </p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWO,SAAS,mBAAmB,EAAE,gBAAgB,EAAE,gBAAgB,EAA2B;IAChG,MAAM,gBAAgB,CAAC;QACrB,IAAI,iBAAiB,QAAQ,CAAC,YAAY;YACxC,iBAAiB,iBAAiB,MAAM,CAAC,CAAA,IAAK,MAAM;QACtD,OAAO;YACL,iBAAiB;mBAAI;gBAAkB;aAAU;QACnD;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC;wBAAE,WAAU;;4BAAwB;4BACxB,iBAAiB,MAAM;4BAAC;4BAAE,iBAAiB,MAAM,KAAK,IAAI,YAAY;;;;;;;;;;;;;0BAIrF,8OAAC;gBAAI,WAAU;0BACZ,yHAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAC;oBACvB,MAAM,aAAa,iBAAiB,QAAQ,CAAC,QAAQ,EAAE;oBACvD,qBACE,8OAAC,kIAAA,CAAA,SAAM;wBAEL,SAAQ;wBACR,SAAS,IAAM,cAAc,QAAQ,EAAE;wBACvC,WAAW,CAAC;;gBAEV,EAAE,aACE,mEACA,gFACH;cACH,CAAC;;4BAEA,4BACC,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CAInB,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,IAAI,CAAC,MAAM,CAAC;;;;;;0CAGvB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAe,QAAQ,IAAI;;;;;;;;;;;;uBArBrC,QAAQ,EAAE;;;;;gBAyBrB;;;;;;YAGD,iBAAiB,MAAM,GAAG,mBACzB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAgC;;;;;;kCAC9C,8OAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC;4BACrB,MAAM,UAAU,yHAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;4BACtD,qBACE,8OAAC;gCAEC,WAAU;0CAET,SAAS;+BAHL;;;;;wBAMX;;;;;;;;;;;;0BAKN,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAwB;;;;;;sCACvC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;YAQ1C,iBAAiB,MAAM,KAAK,mBAC3B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAA0B;;;;;;;;;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 1177, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/components/onboarding/onboarding-complete.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON><PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { CheckCircle, Film, Users, Tv, ArrowRight } from 'lucide-react'\nimport { OnboardingData } from '@/types'\nimport { STREAMING_SERVICES } from '@/utils/constants'\n\ninterface OnboardingCompleteProps {\n  onboardingData: OnboardingData\n  onFinish: () => void\n}\n\nexport function OnboardingComplete({ onboardingData, onFinish }: OnboardingCompleteProps) {\n  const getServiceName = (serviceId: string) => {\n    return STREAMING_SERVICES.find(s => s.id === serviceId)?.name || serviceId\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <CheckCircle className=\"h-16 w-16 text-green-400 mx-auto mb-4\" />\n        <h2 className=\"text-2xl font-bold text-white mb-2\">You're All Set!</h2>\n        <p className=\"text-gray-300 text-lg\">\n          Your personalized SceneSniffer experience is ready. Here's what we've set up for you:\n        </p>\n      </div>\n\n      <div className=\"grid md:grid-cols-3 gap-6\">\n        {/* Genres Summary */}\n        <Card className=\"bg-white/5 border-white/20\">\n          <CardHeader>\n            <CardTitle className=\"text-white flex items-center gap-2\">\n              <Film className=\"h-5 w-5 text-purple-400\" />\n              Genres\n            </CardTitle>\n            <CardDescription className=\"text-gray-300\">\n              {onboardingData.genres.length} selected\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"flex flex-wrap gap-1\">\n              {onboardingData.genres.slice(0, 6).map((genre) => (\n                <span\n                  key={genre}\n                  className=\"px-2 py-1 bg-purple-600/20 text-purple-300 text-xs rounded\"\n                >\n                  {genre}\n                </span>\n              ))}\n              {onboardingData.genres.length > 6 && (\n                <span className=\"px-2 py-1 bg-gray-600/20 text-gray-300 text-xs rounded\">\n                  +{onboardingData.genres.length - 6} more\n                </span>\n              )}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Creators Summary */}\n        <Card className=\"bg-white/5 border-white/20\">\n          <CardHeader>\n            <CardTitle className=\"text-white flex items-center gap-2\">\n              <Users className=\"h-5 w-5 text-purple-400\" />\n              Creators\n            </CardTitle>\n            <CardDescription className=\"text-gray-300\">\n              {onboardingData.creators.length} followed\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-2\">\n              {onboardingData.creators.slice(0, 3).map((creator, index) => (\n                <div key={index} className=\"flex items-center gap-2\">\n                  <span className=\"text-xs text-gray-400 capitalize\">{creator.platform}:</span>\n                  <span className=\"text-white text-sm\">{creator.handle}</span>\n                </div>\n              ))}\n              {onboardingData.creators.length > 3 && (\n                <p className=\"text-gray-400 text-xs\">\n                  +{onboardingData.creators.length - 3} more creators\n                </p>\n              )}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Streaming Services Summary */}\n        <Card className=\"bg-white/5 border-white/20\">\n          <CardHeader>\n            <CardTitle className=\"text-white flex items-center gap-2\">\n              <Tv className=\"h-5 w-5 text-purple-400\" />\n              Streaming\n            </CardTitle>\n            <CardDescription className=\"text-gray-300\">\n              {onboardingData.streaming_services.length} services\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"flex flex-wrap gap-1\">\n              {onboardingData.streaming_services.slice(0, 4).map((serviceId) => (\n                <span\n                  key={serviceId}\n                  className=\"px-2 py-1 bg-blue-600/20 text-blue-300 text-xs rounded\"\n                >\n                  {getServiceName(serviceId)}\n                </span>\n              ))}\n              {onboardingData.streaming_services.length > 4 && (\n                <span className=\"px-2 py-1 bg-gray-600/20 text-gray-300 text-xs rounded\">\n                  +{onboardingData.streaming_services.length - 4} more\n                </span>\n              )}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* What's Next */}\n      <Card className=\"bg-gradient-to-r from-purple-600/20 to-blue-600/20 border-purple-400/30\">\n        <CardHeader>\n          <CardTitle className=\"text-white\">What's Next?</CardTitle>\n          <CardDescription className=\"text-gray-300\">\n            Here's what you can expect from your personalized SceneSniffer experience:\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid md:grid-cols-2 gap-4\">\n            <div className=\"space-y-3\">\n              <div className=\"flex items-start gap-3\">\n                <CheckCircle className=\"h-5 w-5 text-green-400 mt-0.5\" />\n                <div>\n                  <h4 className=\"text-white font-medium\">Personalized Feed</h4>\n                  <p className=\"text-gray-300 text-sm\">\n                    Content from your favorite creators, filtered by your genres\n                  </p>\n                </div>\n              </div>\n              <div className=\"flex items-start gap-3\">\n                <CheckCircle className=\"h-5 w-5 text-green-400 mt-0.5\" />\n                <div>\n                  <h4 className=\"text-white font-medium\">AI Summaries</h4>\n                  <p className=\"text-gray-300 text-sm\">\n                    Quick insights and spoiler-free summaries of content\n                  </p>\n                </div>\n              </div>\n            </div>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-start gap-3\">\n                <CheckCircle className=\"h-5 w-5 text-green-400 mt-0.5\" />\n                <div>\n                  <h4 className=\"text-white font-medium\">Streaming Availability</h4>\n                  <p className=\"text-gray-300 text-sm\">\n                    See where to watch on your selected platforms\n                  </p>\n                </div>\n              </div>\n              <div className=\"flex items-start gap-3\">\n                <CheckCircle className=\"h-5 w-5 text-green-400 mt-0.5\" />\n                <div>\n                  <h4 className=\"text-white font-medium\">Smart Watchlist</h4>\n                  <p className=\"text-gray-300 text-sm\">\n                    Save content and get notified when it's available\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Finish Button */}\n      <div className=\"text-center\">\n        <Button\n          onClick={onFinish}\n          size=\"lg\"\n          className=\"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-3\"\n        >\n          Enter SceneSniffer\n          <ArrowRight className=\"ml-2 h-5 w-5\" />\n        </Button>\n        <p className=\"text-gray-400 text-sm mt-2\">\n          You can always update your preferences later in settings\n        </p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AANA;;;;;;AAaO,SAAS,mBAAmB,EAAE,cAAc,EAAE,QAAQ,EAA2B;IACtF,MAAM,iBAAiB,CAAC;QACtB,OAAO,yHAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,QAAQ;IACnE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,2NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAKvC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAA4B;;;;;;;kDAG9C,8OAAC,gIAAA,CAAA,kBAAe;wCAAC,WAAU;;4CACxB,eAAe,MAAM,CAAC,MAAM;4CAAC;;;;;;;;;;;;;0CAGlC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;wCACZ,eAAe,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,sBACtC,8OAAC;gDAEC,WAAU;0DAET;+CAHI;;;;;wCAMR,eAAe,MAAM,CAAC,MAAM,GAAG,mBAC9B,8OAAC;4CAAK,WAAU;;gDAAyD;gDACrE,eAAe,MAAM,CAAC,MAAM,GAAG;gDAAE;;;;;;;;;;;;;;;;;;;;;;;;kCAQ7C,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAA4B;;;;;;;kDAG/C,8OAAC,gIAAA,CAAA,kBAAe;wCAAC,WAAU;;4CACxB,eAAe,QAAQ,CAAC,MAAM;4CAAC;;;;;;;;;;;;;0CAGpC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;wCACZ,eAAe,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBACjD,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAK,WAAU;;4DAAoC,QAAQ,QAAQ;4DAAC;;;;;;;kEACrE,8OAAC;wDAAK,WAAU;kEAAsB,QAAQ,MAAM;;;;;;;+CAF5C;;;;;wCAKX,eAAe,QAAQ,CAAC,MAAM,GAAG,mBAChC,8OAAC;4CAAE,WAAU;;gDAAwB;gDACjC,eAAe,QAAQ,CAAC,MAAM,GAAG;gDAAE;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/C,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,8LAAA,CAAA,KAAE;gDAAC,WAAU;;;;;;4CAA4B;;;;;;;kDAG5C,8OAAC,gIAAA,CAAA,kBAAe;wCAAC,WAAU;;4CACxB,eAAe,kBAAkB,CAAC,MAAM;4CAAC;;;;;;;;;;;;;0CAG9C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;wCACZ,eAAe,kBAAkB,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,0BAClD,8OAAC;gDAEC,WAAU;0DAET,eAAe;+CAHX;;;;;wCAMR,eAAe,kBAAkB,CAAC,MAAM,GAAG,mBAC1C,8OAAC;4CAAK,WAAU;;gDAAyD;gDACrE,eAAe,kBAAkB,CAAC,MAAM,GAAG;gDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3D,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAa;;;;;;0CAClC,8OAAC,gIAAA,CAAA,kBAAe;gCAAC,WAAU;0CAAgB;;;;;;;;;;;;kCAI7C,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAyB;;;;;;sEACvC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAKzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAyB;;;;;;sEACvC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;8CAM3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAyB;;;;;;sEACvC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAKzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAyB;;;;;;sEACvC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWjD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,MAAK;wBACL,WAAU;;4BACX;0CAEC,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;kCAExB,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;;AAMlD", "debugId": null}}, {"offset": {"line": 1776, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/app/onboarding/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/components/providers/auth-provider'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Progress } from '@/components/ui/progress'\nimport { ArrowRight, ArrowLeft, CheckCircle } from 'lucide-react'\nimport { GenreSelection } from '@/components/onboarding/genre-selection'\nimport { CreatorSelection } from '@/components/onboarding/creator-selection'\nimport { StreamingSelection } from '@/components/onboarding/streaming-selection'\nimport { OnboardingComplete } from '@/components/onboarding/onboarding-complete'\nimport { OnboardingData } from '@/types'\n\nconst STEPS = [\n  { id: 'genres', title: 'Choose Your Genres', description: 'What types of movies and shows do you love?' },\n  { id: 'creators', title: 'Follow Creators', description: 'Which content creators do you trust for recommendations?' },\n  { id: 'streaming', title: 'Streaming Services', description: 'Which platforms do you have access to?' },\n  { id: 'complete', title: 'All Set!', description: 'Your personalized feed is ready' }\n]\n\nexport default function OnboardingPage() {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n  const [currentStep, setCurrentStep] = useState(0)\n  const [onboardingData, setOnboardingData] = useState<OnboardingData>({\n    genres: [],\n    creators: [],\n    streaming_services: []\n  })\n  const [saving, setSaving] = useState(false)\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/auth')\n    }\n  }, [user, loading, router])\n\n  const updateOnboardingData = (key: keyof OnboardingData, value: any) => {\n    setOnboardingData(prev => ({\n      ...prev,\n      [key]: value\n    }))\n  }\n\n  const nextStep = () => {\n    if (currentStep < STEPS.length - 1) {\n      setCurrentStep(currentStep + 1)\n    }\n  }\n\n  const prevStep = () => {\n    if (currentStep > 0) {\n      setCurrentStep(currentStep - 1)\n    }\n  }\n\n  const canProceed = () => {\n    switch (currentStep) {\n      case 0: // Genres\n        return onboardingData.genres.length > 0\n      case 1: // Creators\n        return onboardingData.creators.length > 0\n      case 2: // Streaming\n        return onboardingData.streaming_services.length > 0\n      default:\n        return true\n    }\n  }\n\n  const completeOnboarding = async () => {\n    if (!user) return\n\n    setSaving(true)\n    try {\n      // Save user preferences\n      const response = await fetch('/api/user/preferences', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(onboardingData)\n      })\n\n      if (!response.ok) {\n        throw new Error('Failed to save preferences')\n      }\n\n      nextStep() // Go to completion step\n    } catch (error) {\n      console.error('Error saving onboarding data:', error)\n      alert('Failed to save preferences. Please try again.')\n    } finally {\n      setSaving(false)\n    }\n  }\n\n  const handleFinish = () => {\n    router.push('/feed')\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-white\"></div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null\n  }\n\n  const progress = ((currentStep + 1) / STEPS.length) * 100\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-4\">\n      <div className=\"max-w-4xl mx-auto py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-white mb-2\">Welcome to SceneSniffer!</h1>\n          <p className=\"text-xl text-gray-300\">Let's personalize your experience</p>\n        </div>\n\n        {/* Progress */}\n        <div className=\"mb-8\">\n          <div className=\"flex justify-between items-center mb-2\">\n            <span className=\"text-sm text-gray-300\">Step {currentStep + 1} of {STEPS.length}</span>\n            <span className=\"text-sm text-gray-300\">{Math.round(progress)}% complete</span>\n          </div>\n          <Progress value={progress} className=\"h-2\" />\n        </div>\n\n        {/* Step Content */}\n        <Card className=\"bg-white/10 border-white/20 text-white mb-8\">\n          <CardHeader>\n            <CardTitle className=\"text-2xl\">{STEPS[currentStep].title}</CardTitle>\n            <CardDescription className=\"text-gray-300 text-lg\">\n              {STEPS[currentStep].description}\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            {currentStep === 0 && (\n              <GenreSelection\n                selectedGenres={onboardingData.genres}\n                onGenresChange={(genres) => updateOnboardingData('genres', genres)}\n              />\n            )}\n            {currentStep === 1 && (\n              <CreatorSelection\n                selectedCreators={onboardingData.creators}\n                onCreatorsChange={(creators) => updateOnboardingData('creators', creators)}\n              />\n            )}\n            {currentStep === 2 && (\n              <StreamingSelection\n                selectedServices={onboardingData.streaming_services}\n                onServicesChange={(services) => updateOnboardingData('streaming_services', services)}\n              />\n            )}\n            {currentStep === 3 && (\n              <OnboardingComplete\n                onboardingData={onboardingData}\n                onFinish={handleFinish}\n              />\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Navigation */}\n        {currentStep < 3 && (\n          <div className=\"flex justify-between\">\n            <Button\n              variant=\"outline\"\n              onClick={prevStep}\n              disabled={currentStep === 0}\n              className=\"text-white border-white hover:bg-white hover:text-black\"\n            >\n              <ArrowLeft className=\"mr-2 h-4 w-4\" />\n              Previous\n            </Button>\n\n            {currentStep === 2 ? (\n              <Button\n                onClick={completeOnboarding}\n                disabled={!canProceed() || saving}\n                className=\"bg-purple-600 hover:bg-purple-700\"\n              >\n                {saving ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                    Saving...\n                  </>\n                ) : (\n                  <>\n                    <CheckCircle className=\"mr-2 h-4 w-4\" />\n                    Complete Setup\n                  </>\n                )}\n              </Button>\n            ) : (\n              <Button\n                onClick={nextStep}\n                disabled={!canProceed()}\n                className=\"bg-purple-600 hover:bg-purple-700\"\n              >\n                Next\n                <ArrowRight className=\"ml-2 h-4 w-4\" />\n              </Button>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAeA,MAAM,QAAQ;IACZ;QAAE,IAAI;QAAU,OAAO;QAAsB,aAAa;IAA8C;IACxG;QAAE,IAAI;QAAY,OAAO;QAAmB,aAAa;IAA2D;IACpH;QAAE,IAAI;QAAa,OAAO;QAAsB,aAAa;IAAyC;IACtG;QAAE,IAAI;QAAY,OAAO;QAAY,aAAa;IAAkC;CACrF;AAEc,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACnE,QAAQ,EAAE;QACV,UAAU,EAAE;QACZ,oBAAoB,EAAE;IACxB;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,MAAM,uBAAuB,CAAC,KAA2B;QACvD,kBAAkB,CAAA,OAAQ,CAAC;gBACzB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE;YACT,CAAC;IACH;IAEA,MAAM,WAAW;QACf,IAAI,cAAc,MAAM,MAAM,GAAG,GAAG;YAClC,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,WAAW;QACf,IAAI,cAAc,GAAG;YACnB,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,OAAO,eAAe,MAAM,CAAC,MAAM,GAAG;YACxC,KAAK;gBACH,OAAO,eAAe,QAAQ,CAAC,MAAM,GAAG;YAC1C,KAAK;gBACH,OAAO,eAAe,kBAAkB,CAAC,MAAM,GAAG;YACpD;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,MAAM;QAEX,UAAU;QACV,IAAI;YACF,wBAAwB;YACxB,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,YAAW,wBAAwB;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,eAAe;QACnB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,MAAM,WAAW,AAAC,CAAC,cAAc,CAAC,IAAI,MAAM,MAAM,GAAI;IAEtD,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAIvC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;;wCAAwB;wCAAM,cAAc;wCAAE;wCAAK,MAAM,MAAM;;;;;;;8CAC/E,8OAAC;oCAAK,WAAU;;wCAAyB,KAAK,KAAK,CAAC;wCAAU;;;;;;;;;;;;;sCAEhE,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,OAAO;4BAAU,WAAU;;;;;;;;;;;;8BAIvC,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAY,KAAK,CAAC,YAAY,CAAC,KAAK;;;;;;8CACzD,8OAAC,gIAAA,CAAA,kBAAe;oCAAC,WAAU;8CACxB,KAAK,CAAC,YAAY,CAAC,WAAW;;;;;;;;;;;;sCAGnC,8OAAC,gIAAA,CAAA,cAAW;;gCACT,gBAAgB,mBACf,8OAAC,sJAAA,CAAA,iBAAc;oCACb,gBAAgB,eAAe,MAAM;oCACrC,gBAAgB,CAAC,SAAW,qBAAqB,UAAU;;;;;;gCAG9D,gBAAgB,mBACf,8OAAC,wJAAA,CAAA,mBAAgB;oCACf,kBAAkB,eAAe,QAAQ;oCACzC,kBAAkB,CAAC,WAAa,qBAAqB,YAAY;;;;;;gCAGpE,gBAAgB,mBACf,8OAAC,0JAAA,CAAA,qBAAkB;oCACjB,kBAAkB,eAAe,kBAAkB;oCACnD,kBAAkB,CAAC,WAAa,qBAAqB,sBAAsB;;;;;;gCAG9E,gBAAgB,mBACf,8OAAC,0JAAA,CAAA,qBAAkB;oCACjB,gBAAgB;oCAChB,UAAU;;;;;;;;;;;;;;;;;;gBAOjB,cAAc,mBACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU,gBAAgB;4BAC1B,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;wBAIvC,gBAAgB,kBACf,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,CAAC,gBAAgB;4BAC3B,WAAU;sCAET,uBACC;;kDACE,8OAAC;wCAAI,WAAU;;;;;;oCAAuE;;6DAIxF;;kDACE,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;iDAM9C,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,CAAC;4BACX,WAAU;;gCACX;8CAEC,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtC", "debugId": null}}]}