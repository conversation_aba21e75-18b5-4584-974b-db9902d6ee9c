{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/lib/supabase-server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Server-side Supabase client\nexport const createServerSupabaseClient = async () => {\n  const cookieStore = await cookies()\n\n  return createServerClient(supabaseUrl, supabaseAnonKey, {\n    cookies: {\n      getAll() {\n        return cookieStore.getAll()\n      },\n      setAll(cookiesToSet) {\n        try {\n          cookiesToSet.forEach(({ name, value, options }) =>\n            cookieStore.set(name, value, options)\n          )\n        } catch {\n          // The `setAll` method was called from a Server Component.\n          // This can be ignored if you have middleware refreshing\n          // user sessions.\n        }\n      },\n    },\n  })\n}\n\n// Service role client for admin operations\nexport const createServiceRoleClient = () => {\n  return createServerClient(\n    supabaseUrl,\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\n    {\n      cookies: {\n        getAll() { return [] },\n        setAll() {},\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,6BAA6B;IACxC,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AACF;AAGO,MAAM,0BAA0B;IACrC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EACtB,aACA,QAAQ,GAAG,CAAC,yBAAyB,EACrC;QACE,SAAS;YACP;gBAAW,OAAO,EAAE;YAAC;YACrB,WAAU;QACZ;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/app/api/test-db/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createServerSupabaseClient } from '@/lib/supabase-server'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const supabase = await createServerSupabaseClient()\n\n    // Test database connection\n    const { data: tables, error: tablesError } = await supabase\n      .from('creators')\n      .select('count')\n      .limit(1)\n\n    if (tablesError) {\n      return NextResponse.json({\n        success: false,\n        error: 'Database connection failed',\n        details: tablesError.message\n      })\n    }\n\n    // Test inserting a sample creator\n    const { data: creator, error: insertError } = await supabase\n      .from('creators')\n      .insert({\n        name: '<PERSON><PERSON>',\n        platform: 'youtube',\n        handle: '@mkbhd',\n        verified: true,\n        trust_score: 9.5,\n        follower_count: 20100000\n      })\n      .select()\n      .single()\n\n    if (insertError && insertError.code !== '23505') { // Ignore duplicate key error\n      return NextResponse.json({\n        success: false,\n        error: 'Failed to insert test creator',\n        details: insertError.message\n      })\n    }\n\n    // Test fetching creators\n    const { data: creators, error: fetchError } = await supabase\n      .from('creators')\n      .select('*')\n      .limit(5)\n\n    if (fetchError) {\n      return NextResponse.json({\n        success: false,\n        error: 'Failed to fetch creators',\n        details: fetchError.message\n      })\n    }\n\n    return NextResponse.json({\n      success: true,\n      message: 'Database tests passed!',\n      data: {\n        creatorsCount: creators?.length || 0,\n        sampleCreators: creators?.slice(0, 3) || []\n      }\n    })\n\n  } catch (error) {\n    console.error('Database test error:', error)\n    return NextResponse.json({\n      success: false,\n      error: 'Database test failed',\n      details: error instanceof Error ? error.message : 'Unknown error'\n    })\n  }\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,6BAA0B,AAAD;QAEhD,2BAA2B;QAC3B,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,YACL,MAAM,CAAC,SACP,KAAK,CAAC;QAET,IAAI,aAAa;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;gBACP,SAAS,YAAY,OAAO;YAC9B;QACF;QAEA,kCAAkC;QAClC,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SACjD,IAAI,CAAC,YACL,MAAM,CAAC;YACN,MAAM;YACN,UAAU;YACV,QAAQ;YACR,UAAU;YACV,aAAa;YACb,gBAAgB;QAClB,GACC,MAAM,GACN,MAAM;QAET,IAAI,eAAe,YAAY,IAAI,KAAK,SAAS;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;gBACP,SAAS,YAAY,OAAO;YAC9B;QACF;QAEA,yBAAyB;QACzB,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACjD,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC;QAET,IAAI,YAAY;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;gBACP,SAAS,WAAW,OAAO;YAC7B;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM;gBACJ,eAAe,UAAU,UAAU;gBACnC,gBAAgB,UAAU,MAAM,GAAG,MAAM,EAAE;YAC7C;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD;IACF;AACF", "debugId": null}}]}