{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/lib/supabase-server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Server-side Supabase client\nexport const createServerSupabaseClient = async () => {\n  const cookieStore = await cookies()\n\n  return createServerClient(supabaseUrl, supabaseAnonKey, {\n    cookies: {\n      getAll() {\n        return cookieStore.getAll()\n      },\n      setAll(cookiesToSet) {\n        try {\n          cookiesToSet.forEach(({ name, value, options }) =>\n            cookieStore.set(name, value, options)\n          )\n        } catch {\n          // The `setAll` method was called from a Server Component.\n          // This can be ignored if you have middleware refreshing\n          // user sessions.\n        }\n      },\n    },\n  })\n}\n\n// Service role client for admin operations\nexport const createServiceRoleClient = () => {\n  return createServerClient(\n    supabaseUrl,\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\n    {\n      cookies: {\n        getAll() { return [] },\n        setAll() {},\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,6BAA6B;IACxC,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AACF;AAGO,MAAM,0BAA0B;IACrC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EACtB,aACA,QAAQ,GAAG,CAAC,yBAAyB,EACrC;QACE,SAAS;YACP;gBAAW,OAAO,EAAE;YAAC;YACrB,WAAU;QACZ;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flickster/scenesniffer/src/app/api/user/preferences/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createServerSupabaseClient, createServiceRoleClient } from '@/lib/supabase-server'\nimport { OnboardingData } from '@/types'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const supabase = await createServerSupabaseClient()\n    const { data: { user } } = await supabase.auth.getUser()\n\n    if (!user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n  // Ensure user profile exists\n  const { data: userProfile, error: userError } = await supabase\n    .from('users')\n    .select('id')\n    .eq('id', user.id)\n    .single()\n\n  if (userError && userError.code === 'PGRST116') {\n    // User doesn't exist, create profile using service role\n    const serviceSupabase = createServiceRoleClient()\n    const { error: createError } = await serviceSupabase\n      .from('users')\n      .insert({\n        id: user.id,\n        username: user.email?.split('@')[0] || 'user',\n        avatar_url: user.user_metadata?.avatar_url\n      })\n\n    if (createError) {\n      console.error('Error creating user profile:', createError)\n      return NextResponse.json({ error: 'Failed to create user profile' }, { status: 500 })\n    }\n  }\n\n    const body: OnboardingData = await request.json()\n    const { genres, creators, streaming_services } = body\n\n    // Validate input\n    if (!Array.isArray(genres) || !Array.isArray(creators) || !Array.isArray(streaming_services)) {\n      return NextResponse.json({ error: 'Invalid input format' }, { status: 400 })\n    }\n\n    // Save user preferences\n    const { error: preferencesError } = await supabase\n      .from('user_preferences')\n      .upsert({\n        user_id: user.id,\n        genres,\n        streaming_services,\n        updated_at: new Date().toISOString()\n      })\n\n    if (preferencesError) {\n      console.error('Error saving preferences:', preferencesError)\n      return NextResponse.json({ error: 'Failed to save preferences' }, { status: 500 })\n    }\n\n    // Process creators - add them to the creators table and link to user\n    for (const creator of creators) {\n      try {\n        // First, try to create or get the creator\n        const { data: existingCreator, error: fetchError } = await supabase\n          .from('creators')\n          .select('id')\n          .eq('platform', creator.platform)\n          .eq('handle', creator.handle)\n          .single()\n\n        let creatorId: string\n\n        if (existingCreator) {\n          creatorId = existingCreator.id\n        } else {\n          // Create new creator\n          const { data: newCreator, error: createError } = await supabase\n            .from('creators')\n            .insert({\n              name: creator.handle,\n              platform: creator.platform,\n              handle: creator.handle,\n              verified: false,\n              trust_score: 5.0\n            })\n            .select('id')\n            .single()\n\n          if (createError) {\n            console.error('Error creating creator:', createError)\n            continue // Skip this creator and continue with others\n          }\n\n          creatorId = newCreator.id\n        }\n\n        // Link creator to user\n        const { error: linkError } = await supabase\n          .from('user_creators')\n          .upsert({\n            user_id: user.id,\n            creator_id: creatorId\n          })\n\n        if (linkError && linkError.code !== '23505') { // Ignore duplicate key error\n          console.error('Error linking creator to user:', linkError)\n        }\n      } catch (error) {\n        console.error('Error processing creator:', creator, error)\n        // Continue with other creators\n      }\n    }\n\n    return NextResponse.json({ \n      success: true,\n      message: 'Preferences saved successfully'\n    })\n\n  } catch (error) {\n    console.error('Error in preferences API:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const supabase = await createServerSupabaseClient()\n    const { data: { user } } = await supabase.auth.getUser()\n\n    if (!user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    // Get user preferences\n    const { data: preferences, error: preferencesError } = await supabase\n      .from('user_preferences')\n      .select('*')\n      .eq('user_id', user.id)\n      .single()\n\n    if (preferencesError && preferencesError.code !== 'PGRST116') {\n      console.error('Error fetching preferences:', preferencesError)\n      return NextResponse.json({ error: 'Failed to fetch preferences' }, { status: 500 })\n    }\n\n    // Get user's followed creators\n    const { data: userCreators, error: creatorsError } = await supabase\n      .from('user_creators')\n      .select(`\n        creators (\n          platform,\n          handle,\n          name,\n          verified,\n          trust_score\n        )\n      `)\n      .eq('user_id', user.id)\n\n    if (creatorsError) {\n      console.error('Error fetching creators:', creatorsError)\n      return NextResponse.json({ error: 'Failed to fetch creators' }, { status: 500 })\n    }\n\n    const creators = userCreators?.map(uc => ({\n      platform: uc.creators.platform,\n      handle: uc.creators.handle,\n      name: uc.creators.name,\n      verified: uc.creators.verified,\n      trust_score: uc.creators.trust_score\n    })) || []\n\n    return NextResponse.json({\n      preferences: preferences || {\n        genres: [],\n        streaming_services: []\n      },\n      creators\n    })\n\n  } catch (error) {\n    console.error('Error in preferences GET API:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,6BAA0B,AAAD;QAChD,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAEtD,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEF,6BAA6B;QAC7B,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACnD,IAAI,CAAC,SACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,aAAa,UAAU,IAAI,KAAK,YAAY;YAC9C,wDAAwD;YACxD,MAAM,kBAAkB,CAAA,GAAA,kIAAA,CAAA,0BAAuB,AAAD;YAC9C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,gBAClC,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,IAAI,KAAK,EAAE;gBACX,UAAU,KAAK,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;gBACvC,YAAY,KAAK,aAAa,EAAE;YAClC;YAEF,IAAI,aAAa;gBACf,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAAgC,GAAG;oBAAE,QAAQ;gBAAI;YACrF;QACF;QAEE,MAAM,OAAuB,MAAM,QAAQ,IAAI;QAC/C,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,kBAAkB,EAAE,GAAG;QAEjD,iBAAiB;QACjB,IAAI,CAAC,MAAM,OAAO,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,aAAa,CAAC,MAAM,OAAO,CAAC,qBAAqB;YAC5F,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAuB,GAAG;gBAAE,QAAQ;YAAI;QAC5E;QAEA,wBAAwB;QACxB,MAAM,EAAE,OAAO,gBAAgB,EAAE,GAAG,MAAM,SACvC,IAAI,CAAC,oBACL,MAAM,CAAC;YACN,SAAS,KAAK,EAAE;YAChB;YACA;YACA,YAAY,IAAI,OAAO,WAAW;QACpC;QAEF,IAAI,kBAAkB;YACpB,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA6B,GAAG;gBAAE,QAAQ;YAAI;QAClF;QAEA,qEAAqE;QACrE,KAAK,MAAM,WAAW,SAAU;YAC9B,IAAI;gBACF,0CAA0C;gBAC1C,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACxD,IAAI,CAAC,YACL,MAAM,CAAC,MACP,EAAE,CAAC,YAAY,QAAQ,QAAQ,EAC/B,EAAE,CAAC,UAAU,QAAQ,MAAM,EAC3B,MAAM;gBAET,IAAI;gBAEJ,IAAI,iBAAiB;oBACnB,YAAY,gBAAgB,EAAE;gBAChC,OAAO;oBACL,qBAAqB;oBACrB,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,YACL,MAAM,CAAC;wBACN,MAAM,QAAQ,MAAM;wBACpB,UAAU,QAAQ,QAAQ;wBAC1B,QAAQ,QAAQ,MAAM;wBACtB,UAAU;wBACV,aAAa;oBACf,GACC,MAAM,CAAC,MACP,MAAM;oBAET,IAAI,aAAa;wBACf,QAAQ,KAAK,CAAC,2BAA2B;wBACzC,UAAS,6CAA6C;oBACxD;oBAEA,YAAY,WAAW,EAAE;gBAC3B;gBAEA,uBAAuB;gBACvB,MAAM,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAChC,IAAI,CAAC,iBACL,MAAM,CAAC;oBACN,SAAS,KAAK,EAAE;oBAChB,YAAY;gBACd;gBAEF,IAAI,aAAa,UAAU,IAAI,KAAK,SAAS;oBAC3C,QAAQ,KAAK,CAAC,kCAAkC;gBAClD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B,SAAS;YACpD,+BAA+B;YACjC;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,6BAA0B,AAAD;QAChD,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAEtD,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,uBAAuB;QACvB,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,gBAAgB,EAAE,GAAG,MAAM,SAC1D,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,MAAM;QAET,IAAI,oBAAoB,iBAAiB,IAAI,KAAK,YAAY;YAC5D,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA8B,GAAG;gBAAE,QAAQ;YAAI;QACnF;QAEA,+BAA+B;QAC/B,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACxD,IAAI,CAAC,iBACL,MAAM,CAAC,CAAC;;;;;;;;MAQT,CAAC,EACA,EAAE,CAAC,WAAW,KAAK,EAAE;QAExB,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA2B,GAAG;gBAAE,QAAQ;YAAI;QAChF;QAEA,MAAM,WAAW,cAAc,IAAI,CAAA,KAAM,CAAC;gBACxC,UAAU,GAAG,QAAQ,CAAC,QAAQ;gBAC9B,QAAQ,GAAG,QAAQ,CAAC,MAAM;gBAC1B,MAAM,GAAG,QAAQ,CAAC,IAAI;gBACtB,UAAU,GAAG,QAAQ,CAAC,QAAQ;gBAC9B,aAAa,GAAG,QAAQ,CAAC,WAAW;YACtC,CAAC,MAAM,EAAE;QAET,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,aAAa,eAAe;gBAC1B,QAAQ,EAAE;gBACV,oBAAoB,EAAE;YACxB;YACA;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}